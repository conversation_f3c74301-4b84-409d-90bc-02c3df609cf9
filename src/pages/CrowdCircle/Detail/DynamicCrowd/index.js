import React, { useState, useEffect } from 'react';
import { Card, Form, Input, DatePicker, Select, Button, message, Spin } from 'antd';
import { connect } from 'dva';
import { Link, history } from 'umi';
import dayjs from 'dayjs';
import { createCrowd, queryCrowdCircle, updateCrowdCircle } from '@/services/api';
import { getQueryParams } from '../../common/utils';
import AddCrowdGroupModal from '../../components/AddCrowdGroupModal';
import styles from './index.less';

const { TextArea } = Input;
const { Option } = Select;

const DynamicCrowd = props => {
  const {
    dispatch,
    crowdGroup: { dataList: groupList = [], modelConfig },
  } = props;

  const [form] = Form.useForm();
  const [crowdGroupForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);

  const { id, type } = getQueryParams();
  const isEdit = !!id;
  const isView = type === 'view';

  // 获取人群分组数据
  useEffect(() => {
    dispatch({
      type: 'crowdGroup/fetchData',
      payload: {
        pageNo: 1,
        pageSize: 500,
      },
    });
  }, [dispatch]);

  // 编辑时获取人群详情
  useEffect(() => {
    if (isEdit) {
      setDetailLoading(true);
      queryCrowdCircle(id).then(res => {
        if (res.success && res.data) {
          const data = res.data;
          form.setFieldsValue({
            crowdName: data.crowdName,
            crowdDescription: data.crowdDescription,
            expiredDate: data.expiredDate ? dayjs(data.expiredDate) : null,
            groupIds: data.groupIds || [],
          });
        }
        setDetailLoading(false);
      });
    }
  }, [id, isEdit, form]);

  // 新增分组处理
  const handleFinish = values => {
    const data = {
      ...values,
      type: 'CROWD',
    };
    if (modelConfig.isEdit) {
      data.id = modelConfig.record.key;
      data.name = modelConfig.record.title;
    }
    dispatch({
      type: 'crowdGroup/operateCrowdGroup',
      payload: { ...data, isEdit: modelConfig.isEdit },
    }).then(res => {
      if (res.success) {
        crowdGroupForm.resetFields();
      }
    });
  };

  // 表单提交
  const onFinish = values => {
    const payload = {
      ...values,
      crowdType: 'DYNAMIC_CROWD',
      physicalProfileCode: 'TAOBAO_USER',
      needUpdate: false,
      expiredDate: values.expiredDate.valueOf(),
      applyScenes: ['MATCH'],
    };

    if (isEdit) {
      payload.id = id;
    }

    setLoading(true);
    const request = isEdit ? updateCrowdCircle : createCrowd;

    request(payload).then(res => {
      if (res.success) {
        message.success(isEdit ? '更新成功' : '创建成功');
        history.push('/crowd-stategy/gather-person');
      } else {
        message.error(res.msg || (isEdit ? '更新失败' : '创建失败'));
      }
      setLoading(false);
    });
  };

  return (
    <Spin spinning={detailLoading}>
      <Form form={form} onFinish={onFinish} layout="vertical">
        <div className={styles.container}>
          <div className={styles.leftCol}>
            <Card title="人群信息">
              <Form.Item
                name="crowdName"
                label="人群名称"
                rules={[
                  { required: true, message: '请输入人群名称' },
                  { max: 50, message: '人群名称不能超过50个字符' },
                ]}
              >
                <Input placeholder="请输入人群名称" disabled={isView} />
              </Form.Item>

              <Form.Item
                name="crowdDescription"
                label="人群描述"
                rules={[
                  { required: true, message: '请输入人群描述' },
                  { max: 200, message: '人群描述不能超过200个字符' },
                ]}
              >
                <TextArea
                  placeholder="请输入人群描述"
                  rows={3}
                  showCount
                  maxLength={200}
                  disabled={isView}
                />
              </Form.Item>

              <Form.Item
                name="expiredDate"
                label="人群过期时间"
                rules={[{ required: true, message: '请选择人群过期时间' }]}
              >
                <DatePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择人群过期时间"
                  style={{ width: '100%' }}
                  disabledDate={current => current && current < dayjs().startOf('day')}
                  disabled={isView}
                />
              </Form.Item>

              <Form.Item
                name="groupIds"
                label="人群分组"
                rules={[{ required: true, message: '请选择人群分组' }]}
              >
                <div style={{ display: 'flex', gap: '8px' }}>
                  <Select
                    mode="multiple"
                    placeholder="请选择人群分组"
                    style={{ flex: 1 }}
                    disabled={isView}
                  >
                    {groupList.map(group => (
                      <Option key={group.key} value={group.key}>
                        {group.title}
                      </Option>
                    ))}
                  </Select>
                  {!isView && (
                    <Button
                      type="primary"
                      onClick={() => {
                        dispatch({
                          type: 'crowdGroup/updateState',
                          payload: {
                            modelConfig: {
                              visible: true,
                              isEdit: false,
                              record: {},
                            },
                          },
                        });
                      }}
                    >
                      新增分组
                    </Button>
                  )}
                </div>
              </Form.Item>

              <Form.Item label="人群包类型">
                <span>淘宝id</span>
              </Form.Item>

              <Form.Item label="应用场景">
                <span>人群匹配</span>
              </Form.Item>
            </Card>
          </div>

          <div className={styles.rightCol}>
            <Card title="操作">
              <div className={styles.rightBtn}>
                {!isView && (
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    style={{ marginRight: 12 }}
                  >
                    {isEdit ? '更新' : '创建'}
                  </Button>
                )}
                <Link to="/crowd-stategy/gather-person">
                  <Button>返回</Button>
                </Link>
              </div>
            </Card>
          </div>
        </div>

        {/* 新增分组弹窗 */}
        {modelConfig.visible && (
          <AddCrowdGroupModal handleFinish={handleFinish} form={crowdGroupForm} />
        )}
      </Form>
    </Spin>
  );
};

export default connect(({ crowdGroup }) => ({
  crowdGroup,
}))(DynamicCrowd);
