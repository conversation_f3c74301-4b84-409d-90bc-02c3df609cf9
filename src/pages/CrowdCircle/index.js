import React, { useState, useEffect, Fragment } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Button,
  Divider,
  Popconfirm,
  Badge,
  Tag,
  Tooltip,
  Dropdown,
  Menu,
  Tabs,
  Form,
  Modal,
} from 'antd';
import dayjs from 'dayjs';
import { connect } from 'dva';
import { Link, history } from 'umi';
import {
  QuestionCircleOutlined,
  ExportOutlined,
  UpCircleFilled,
  DownCircleFilled,
  EditOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';

import PageHeaderWrapper from '@/components/PageHeaderWrapper';
import { CROWD_PROGRESS_STATUS, CROWD_PROCESS_TIME } from '@/constants';

import CreateCrowdModal1 from '@/components/CreateCrowdModal1';
import CrowdDelayModal from './components/CrowdDelayModal';
import CrowdTestModal from './components/CrowdTestModal';
import CrowdExportModal from '@/components/CrowdExportModal';
import CrowdMonitorModal from './components/CrowdMonitorModal';
import AddGroupModel from './components/AddGroupModel';
import CrowdTransferModal from './components/CrowdTransferModal';
import CreateOperateCrowdModal from './components/CreateOperateCrowdModal';
import CreateDynamicCrowdModal from './components/CreateDynamicCrowdModal';
import CrowdBlackOrWhiteList from '@/components/CrowdBlackOrWhiteList';
import { PROFILE_TYPE, approvalApplySceneEnum, ProfileCodeEnum, crowdErrMsgMap } from './constants';
import { goBpms, goApprovalBpms } from './common/utils';
import styles from './index.less';
import { get } from 'lodash';
import LeftTree from './LeftTree';
import MyIcon from '@/components/MyIcon';
import SearchFrorm from './SearchForm';
import { title } from '@/defaultSettings';
import { MinusCircleOutlined, PlusCircleOutlined, WarningFilled } from '@ant-design/icons';
import { zhugeUrl } from '@/utils/utils';
import CrowdDetailModal from './components/CrowdDetailModal';
import UpdateOwner from './components/UpdateOwner';
import CrowdGroupModal from './components/CrowdGroupModal';

const { confirm } = Modal;

let classifyTabList = [
  {
    label: '我的人群',
    value: 'MY_CROWDS',
  },
  {
    label: '全部人群',
    value: 'ALL',
  },
];

const CrowdCircle = props => {
  // State hooks
  const [isRealTime, setIsRealTime] = useState(false);
  const [isOwner, setIsOwner] = useState(false);
  const [isFold, setIsFold] = useState(false);
  const [createCrowdType, setCreateCrowdType] = useState('');
  const [createCrowdVisible, setCreateCrowdVisible] = useState(false);
  const [updateCrowdGroupConfig, setUpdateCrowdGroupConfig] = useState({
    visible: false,
    record: {},
  });
  const [form] = Form.useForm();
  const [crowdGroupForm] = Form.useForm();

  const {
    dispatch,
    user: { currentUser, isSuperAdmin },
    crowdCircle: {
      crowdDelayModalVisible,
      crowdTestModalVisible,
      crowdExportModalVisible,
      blackOrWhiteListModalVisible,
      crowdMonitorVisible,
      addGroupVisible,
      crowdTransferVisible,
      record,
      loading,
      dataSource,
      tabCntMap,
      searchParams,
      modelConfig,
      tabKey,
      isChildSearch,
      expandedRowKeys,
      detailModalVisible,
      curCrowd,
      refreshLoading,
      crowdByOperateModalVisible,
      editCrowdByOperateFormData,
      dynamicCrowdModalVisible,
      editDynamicCrowdFormData,
      detailLoading,
      bucketLoading,
    },
    crowdGroup: {
      dataList, // 人群分组
    },
  } = props;

  useEffect(() => {
    // 检查是否是页面刷新
    const isPageRefresh = () => {
      // 使用 performance.navigation 检测页面刷新
      if (window.performance && window.performance.navigation) {
        return window.performance.navigation.type === 1; // TYPE_RELOAD
      }
      // 备用方案：检查 sessionStorage 中的导航标记
      const navigationFlag = sessionStorage.getItem('crowdCircle_navigation');
      if (!navigationFlag || navigationFlag === 'true') {
        // 首次访问或刷新
        return true;
      }
      // 如果是正常导航，重置标记为已访问
      sessionStorage.setItem('crowdCircle_navigation', 'visited');
      return false;
    };

    // 监听页面卸载事件，用于区分刷新和正常离开
    const handleBeforeUnload = () => {
      // 页面即将卸载时，设置标记
      sessionStorage.setItem('crowdCircle_beforeUnload', 'true');
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // 如果是页面刷新，清除保存的筛选条件
    if (isPageRefresh()) {
      try {
        localStorage.removeItem('crowdCircle_searchParams');
      } catch (error) {
        console.error('清除筛选条件失败:', error);
      }
      defaultFetchData();
      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }

    // 尝试从 localStorage 加载保存的筛选条件（仅在非刷新情况下）
    const loadSavedSearchParams = () => {
      try {
        const saved = localStorage.getItem('crowdCircle_searchParams');
        return saved ? JSON.parse(saved) : {};
      } catch (error) {
        console.error('加载保存的筛选条件失败:', error);
        return {};
      }
    };

    const savedParams = loadSavedSearchParams();

    // 如果有保存的筛选条件，使用这些条件进行初始查询
    if (Object.keys(savedParams).length > 0) {
      const savedTabKey = savedParams.tabKey || tabKey || 'MY_CROWDS';

      // 先更新 tabKey 状态
      dispatch({
        type: 'crowdCircle/updateState',
        payload: {
          tabKey: savedTabKey,
        },
      });

      // 如果有分组信息，同时恢复分组选中状态
      if (savedParams.categoryId) {
        dispatch({
          type: 'crowdGroup/updateState',
          payload: {
            categoryId: savedParams.categoryId,
          },
        });
      }

      dispatch({
        type: 'crowdCircle/pageQuery',
        payload: {
          ...savedParams,
          pageNo: savedParams.pageNo || 1,
          pageSize: savedParams.pageSize || 10,
          tabKey: savedTabKey,
        },
      });
    } else {
      defaultFetchData();
    }

    // 清理函数
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const defaultFetchData = () => {
    dispatch({
      type: 'crowdCircle/pageQuery',
      payload: {
        pageNo: 1,
        pageSize: 10,
        tabKey: tabKey ?? 'MY_CROWDS',
      },
    });
  };

  const handlehistoryPage = (record, type) => {
    // 在页面跳转时设置导航标记，表示不是刷新而是正常跳转
    sessionStorage.setItem('crowdCircle_navigation', 'navigation');

    if (record.crowdType === 'OPERATE_CROWD') {
      dispatch({
        type: 'crowdCircle/queryCrowdById',
        payload: {
          id: record.id,
          isEdit: type === 'edit',
          isDetail: true,
        },
      });
      return;
    }
    if (type === 'view') {
      window.open(
        zhugeUrl +
          `/crowd-stategy/gather-person/detail?crowdType=${record.circleType}&profileType=${record.physicalProfileCode}&id=${record.id}&type=view`
      );
      return;
    }
    history.push(
      `/crowd-stategy/gather-person/detail?crowdType=${record.circleType}&profileType=${record.physicalProfileCode}&id=${record.id}&type=edit`
    );
  };

  function isDateFutureOrToday(date) {
    const today = dayjs().startOf('day');
    const inputDate = dayjs(date).startOf('day');

    if (!inputDate.isValid()) {
      return false;
    }

    return inputDate.isAfter(today) || inputDate.isSame(today);
  }

  // Columns
  const columns = [
    {
      title: '人群ID',
      dataIndex: 'id',
      width: 100,
      align: 'center',
    },
    {
      title: '人群名称',
      dataIndex: 'crowdName',
      width: 220,
      render: (text, record) => {
        return (
          <div>
            {record.timeType === 'ONLINE' ? (
              <Tag className={styles.realTimeTag} color="volcano">
                实时
              </Tag>
            ) : null}
            {record.crowdType === 'BUCKET_CHILD' ? (
              text
            ) : (
              <a onClick={() => handlehistoryPage(record, 'view')}>{text}</a>
            )}
          </div>
        );
      },
    },
    {
      title: '人群类型',
      dataIndex: 'profileCode',
      width: 90,
      render: (text, record) => {
        return ProfileCodeEnum[text];
      },
    },
    {
      title: '圈选方式',
      dataIndex: 'circleType',
      width: 90,
      render: text => {
        return (
          <Tooltip title={text.length > 4 ? PROFILE_TYPE[text] : null}>
            {PROFILE_TYPE[text]}
          </Tooltip>
        );
      },
    },
    {
      title: '人群数量',
      dataIndex: 'crowdAmount',
      width: 130,
      render: (text, record) => {
        if (record.timeType === 'ONLINE') {
          return '实时人群不展示';
        } else if (!text) {
          return '暂无';
        } else {
          return text;
        }
      },
    },
    {
      title: '人群状态',
      dataIndex: 'crowdStatus',
      width: 100,
      render: (crowdStatus, record) => {
        const obj = CROWD_PROGRESS_STATUS[crowdStatus];
        const errMsg = get(record, 'extInfo.crowdBuildRecord', null)
          ? get(record, 'extInfo.crowdBuildRecord.errMsg', null)
          : null;
        const approvalObj =
          CROWD_PROGRESS_STATUS[get(record, 'approvalInfo', {})?.approvalStatus] || undefined;
        if (!obj) return '';
        return (
          <>
            <span style={{ display: 'flex', alignItems: 'center' }}>
              {crowdStatus === 'ERROR' && obj ? (
                <div>
                  <Tooltip
                    title={
                      errMsg ||
                      (record.errorCode
                        ? crowdErrMsgMap[record.errorCode]
                        : `预计运行耗时: ${CROWD_PROCESS_TIME[record.crowdType]}`)
                    }
                  >
                    <Tag color={obj.color}>{obj.text}</Tag>
                  </Tooltip>
                  <div
                    style={{ color: 'red', cursor: 'pointer' }}
                    onClick={() => window.open('https://yuque.antfin.com/qnwrq9/kzl68s/bpqsue#%20')}
                  >
                    错误自查
                  </div>
                </div>
              ) : (
                <Tag
                  color={obj.color}
                  onClick={() => {
                    goBpms(record);
                  }}
                >
                  {obj.text}
                </Tag>
              )}
              <Link
                to={`/crowd-stategy/operation-gather-person?id=${record.id}&type=${
                  tabKey === 'MY_CROWDS' ? 'primary' : 'all'
                }`}
              >
                <ExportOutlined />
              </Link>
            </span>
            {approvalObj && (
              <div style={{ marginTop: 10 }}>
                <Tag
                  color={approvalObj?.color}
                  onClick={() => {
                    goApprovalBpms(record);
                  }}
                  style={{ cursor: 'pointer' }}
                >
                  {approvalObj.text}
                </Tag>
              </div>
            )}
          </>
        );
      },
    },
    {
      title: '上次构建时间',
      dataIndex: 'gmtLastBuilt',
      width: 120,
      render: text => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '暂无'),
    },
    {
      title: '过期时间',
      dataIndex: 'expiredDate',
      width: 120,
      render: text => {
        const isExpired = Date.now() > text;
        const isExpired7Day = Date.now() > text - 7 * 24 * 60 * 60 * 1000;
        return (
          <>
            {isExpired7Day && !isExpired ? <WarningFilled style={{ color: 'red' }} /> : null}
            {isExpired ? (
              <Badge status="error" text="已过期" />
            ) : (
              dayjs(text).format('YYYY-MM-DD HH:mm:ss')
            )}
          </>
        );
      },
    },
    {
      title: () => {
        return (
          <div className={styles.columnTitle}>
            负责人
            <Tooltip title="人群的唯一负责人">
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        );
      },
      dataIndex: 'owner',
      width: 100,
      render: (text, record) => {
        const { creator = {}, operator = [], owner = {} } = record;
        const owners = [...operator, creator, owner];
        const isGranted = owners.map(o => o.nickName).includes(currentUser.name) || isSuperAdmin;
        const isBucketChild = record && record.crowdType === 'BUCKET_CHILD';
        return (
          <div style={{ cursor: !isGranted || isBucketChild ? 'not-allowed' : 'pointer' }}>
            {text?.nickName ?? ''}
            <EditOutlined
              style={{ cursor: !isGranted || isBucketChild ? 'not-allowed' : 'pointer' }}
              onClick={() => {
                if (!isGranted || isBucketChild) {
                  return;
                }
                dispatch({
                  type: 'crowdCircle/updateState',
                  payload: {
                    modelConfig: {
                      visible: true,
                      isOwner: true,
                      record,
                    },
                  },
                });
                form.setFieldsValue({
                  owner: text,
                });
              }}
              twoToneColor={'#000'}
            />
          </div>
        );
      },
    },
    {
      title: () => {
        return (
          <div className={styles.columnTitle}>
            管理员
            <Tooltip title="拥有和负责人相同的人群编辑权限（唯独不含“修改负责人”和“删除”权限）">
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        );
      },
      dataIndex: 'managers',
      width: 160,
      render: (text, record) => {
        const { creator = {}, operator = [], owner = {}, managers = [] } = record;
        const owners = [...operator, creator, owner, ...managers];
        const isGranted = owners.map(o => o.nickName).includes(currentUser.name) || isSuperAdmin;
        const isBucketChild = record && record.crowdType === 'BUCKET_CHILD';
        return (
          <div style={{ cursor: !isGranted || isBucketChild ? 'not-allowed' : 'pointer' }}>
            {text && text.map(t => t.nickName).join(',')}
            <EditOutlined
              style={{ cursor: !isGranted || isBucketChild ? 'not-allowed' : 'pointer' }}
              onClick={() => {
                if (!isGranted || isBucketChild) {
                  return;
                }
                dispatch({
                  type: 'crowdCircle/updateState',
                  payload: {
                    modelConfig: {
                      visible: true,
                      isOwner: false,
                      record,
                    },
                  },
                });

                form.setFieldsValue({
                  owner: text,
                });
              }}
              twoToneColor={'#000'}
            />
          </div>
        );
      },
    },
    {
      title: '上次修改时间',
      dataIndex: 'editTime',
      width: 136,
      render: text => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      width: 180,
      fixed: 'right',
      render: (text, record) => {
        const { creator = {}, operator = [], owner = {}, managers = [] } = record;
        const owners = [...operator, creator, owner, ...managers];
        const isGranted = owners.map(o => o.nickName).includes(currentUser.name) || isSuperAdmin;
        const isBucketChild = record && record.crowdType === 'BUCKET_CHILD';
        const isExpired = Date.now() > record.expiredDate;
        const isToday = isDateFutureOrToday(record.gmtCreate);
        return (
          <>
            <Tooltip
              title={
                record.crowdType === 'BUCKET_CHILD' &&
                '请对实验父人群进行延期操作即可，其中实验子人群过期时间默认和父人群保持'
              }
            >
              <Button
                size="small"
                disabled={!isGranted || record.crowdType === 'BUCKET_CHILD'}
                type="link"
                onClick={() => onOpenCrowdDelayModal(record)}
              >
                延期
              </Button>
            </Tooltip>
            <Divider type="vertical" style={{ marginLeft: '0px', marginRight: '0px' }} />
            <Button
              disabled={!isGranted || isBucketChild || record.crowdType === 'BUCKET_CHILD'}
              type="link"
              onClick={() => {
                handlehistoryPage(record, 'edit');
              }}
              size="small"
            >
              编辑
            </Button>
            <Divider type="vertical" style={{ marginLeft: '0px', marginRight: '0px' }} />
            <Dropdown
              overlay={menu(record, isGranted, isSuperAdmin, isExpired, isToday)}
              placement="bottomCenter"
            >
              <Button size="small" type="link">
                更多
              </Button>
            </Dropdown>
          </>
        );
      },
    },
  ];

  const menu = (record, isGranted, isSuperAdmin, isExpired, isToday) => (
    <Menu>
      <Menu.Item key="1">
        <Button type="link" onClick={() => onOpenCrowdTestModal(record)}>
          测试
        </Button>
      </Menu.Item>
      <Menu.Item key="2">
        {record.timeType === 'ONLINE' && (
          <>
            <Button type="link" disabled>
              导出
            </Button>
            <Tooltip title="实时不支持导出">
              <QuestionCircleOutlined />
            </Tooltip>
          </>
        )}
        {record.timeType !== 'ONLINE' && (
          <>
            <Button
              type="link"
              onClick={() => onOpenCrowdExportModal(record, isGranted)}
              disabled={(!isGranted && !isSuperAdmin) || ['DEVICE'].includes(record.profileType)}
            >
              导出
            </Button>
            {record.exported ? (
              <Tooltip title={<span>已导出</span>}>
                <QuestionCircleOutlined />
              </Tooltip>
            ) : null}
          </>
        )}
      </Menu.Item>
      <Menu.Item key="3">
        <Popconfirm
          title="会覆盖已有人群数据，请知悉"
          onConfirm={() => reRun(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button type="link" disabled={!isGranted && !isSuperAdmin}>
            重跑
          </Button>
        </Popconfirm>
      </Menu.Item>
      {record.circleType !== 'BUCKET_CHILD' && (
        <Menu.Item key="8">
          <Tooltip title={isToday ? '当日新建人群请第二日再进行查询' : ''}>
            <Button
              disabled={isToday}
              onClick={() => {
                window.open(
                  zhugeUrl +
                    `/crowd-manage/human-relations?crowdId=${record.id}&crowdName=${record.crowdName}`
                );
              }}
              type="link"
            >
              血缘关系
            </Button>
          </Tooltip>
        </Menu.Item>
      )}
      {record.crowdType === 'ALGO_CROWD' ||
      record.crowdType === 'TRIP_GALAXY_CROWD' ||
      record.crowdType === 'FILE_CROWD' ||
      record.crowdType === 'ALGO_ENLARGE_CROWD' ? null : (
        <Menu.Item key="4">
          <Button
            type="link"
            onClick={() => onAddGroup(record)}
            disabled={
              (!isGranted && !isSuperAdmin) ||
              (record.crowdBucketInfo && record.crowdBucketInfo.parentId)
            }
          >
            实验分组
          </Button>
        </Menu.Item>
      )}
      <Menu.Item key="5">
        <>
          <Button
            type="link"
            disabled={
              (!isGranted && !isSuperAdmin) ||
              !(record.crowdApplyScene && record.crowdApplyScene.includes('MATCH')) ||
              ['DEVICE'].includes(record.profileType)
            }
            // style={
            //   isGranted && (record.crowdApplyScene || []).includes('MATCH')
            //     ? { color: '#666' }
            //     : null
            // }
            onClick={() => onOpenCrowdBlackOrWhiteListModal(record)}
          >
            黑白名单
          </Button>
          <Tooltip title="人群匹配才能配置黑白名单">
            {!(record.crowdApplyScene || []).includes('MATCH') && <QuestionCircleOutlined />}
          </Tooltip>
        </>
      </Menu.Item>
      {record.circleType !== 'BUCKET_CHILD' && (
        <Menu.Item key="6">
          <Button
            type="link"
            onClick={() => onCrowdMonitor(record)}
            disabled={(!isGranted && !isSuperAdmin) || isExpired}
          >
            监控告警
          </Button>
        </Menu.Item>
      )}
      <Menu.Item key="7">
        <Button
          type="link"
          onClick={() => onDelete(record.id)}
          danger
          disabled={(!isGranted && !isSuperAdmin) || record.crowdType === 'BUCKET_CHILD'}
        >
          删除
        </Button>
      </Menu.Item>
      {/* <Menu.Item>
        <Button
          type="link"
          onClick={() => onOpenCrowdTransfer(record)}
          disabled={!isGranted && !isSuperAdmin}
        >
          转交人群
        </Button>
      </Menu.Item> */}
    </Menu>
  );

  const expandedRowRender = record => {
    return (
      <>
        {record.isExperiment && <Button style={{ marginBottom: 16 }}>实验分组</Button>}
        <Table
          columns={columns.filter(item => item.dataIndex !== 'gmtLastBuilt')}
          pagination={false}
          scroll={{ x: 1300 }}
          rowSelection={
            window.location.href.includes('needRadio=true')
              ? {
                  type: 'radio',
                  onChange: (selectedRowKeys, selectedRows) => {
                    const obj = { id: selectedRowKeys[0], name: selectedRows[0].crowdName };
                    window.parent.postMessage(obj, '*');
                  },
                }
              : null
          }
          dataSource={record.childCrowds}
          rowKey="id"
        />
      </>
    );
  };

  const onOpenCrowdDelayModal = record => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        record,
        crowdDelayModalVisible: true,
      },
    });
  };

  const onCrowdDelayModalCancel = () => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        crowdDelayModalVisible: false,
        record: {},
      },
    });
  };

  const onSubmitDelayTime = values => {
    dispatch({
      type: 'crowdCircle/editCrowdDelayTime',
      payload: values,
    });
  };

  const onOpenCrowdTestModal = record => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        record,
        crowdTestModalVisible: true,
      },
    });
  };

  const onCrowdTestModalCancel = () => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        crowdTestModalVisible: false,
        record: {},
      },
    });
  };

  const onOpenCrowdExportModal = (record, isGranted) => {
    setIsRealTime(!!record.realTime);
    setIsOwner(isGranted);
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        record,
        crowdExportModalVisible: true,
      },
    });
  };

  const onCrowdExportModalClose = () => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        crowdExportModalVisible: false,
        record: {},
      },
    });
  };

  const onSubmitExportCrowd = values => {
    dispatch({
      type: 'crowdCircle/editExportCrowdTask',
      payload: values,
    });
  };

  const onDelete = id => {
    confirm({
      icon: <ExclamationCircleOutlined />,
      title: '提示',
      content: '删除人群会将子人群一起删除，确定删除吗？',
      onOk: () => {
        dispatch({
          type: 'crowdCircle/delete',
          payload: {
            id,
          },
        });
      },
    });
  };

  const reRun = crowdId => {
    dispatch({
      type: 'crowdCircle/reRun',
      payload: {
        crowdId,
      },
    });
  };

  const onOpenCrowdBlackOrWhiteListModal = record => {
    const { owner = {}, id } = record;
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        record: {
          creator: owner.nickName,
          groupId: id,
          operator: owner.nickName,
        },
        blackOrWhiteListModalVisible: true,
      },
    });
    dispatch({
      type: 'crowdCircle/queryblackwhitelist',
      payload: {
        groupId: record.id,
        groupType: 'TAOBAO_USER',
      },
    });
  };

  const onCrowdBlackOrWhiteListModalClose = () => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        blackOrWhiteListModalVisible: false,
        record: {},
      },
    });
  };

  const onSubmitBlackOrWhiteListCrowd = values => {
    dispatch({
      type: 'crowdCircle/editBlackWhiteListCrowdTask',
      payload: {
        ...values,
        operator: currentUser.name,
      },
    });
  };

  const onCrowdMonitor = record => {
    dispatch({
      type: 'crowdCircle/queryMonitorByCrowd',
      payload: {
        id: record.id,
      },
    }).then(res => {
      if (res) {
        dispatch({
          type: 'crowdCircle/updateState',
          payload: {
            record: {
              list: res,
              id: record.id,
            },
            crowdMonitorVisible: true,
          },
        });
      }
    });
  };

  const onCrowdMonitorCancel = record => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        record: {},
        crowdMonitorVisible: false,
      },
    });
  };

  const onConfirmMonitor = value => {
    dispatch({
      type: 'crowdCircle/confirmMonitorUpdate',
      payload: {
        crowdId: record.id,
        configList: value,
      },
    });
  };

  const onAddGroup = record => {
    const { id } = record;
    const operator = {
      empId: currentUser.workId,
      nickName: currentUser.name,
    };

    if (record.crowdBucketInfo) {
      dispatch({
        type: 'crowdCircle/updateState',
        payload: {
          addGroupVisible: true,
          record: {
            parentId: id,
            crowdBucketInfo: record.crowdBucketInfo,
            operator,
          },
        },
      });
    } else {
      dispatch({
        type: 'crowdCircle/updateState',
        payload: {
          addGroupVisible: true,
          record: {
            parentId: id,
            operator,
          },
        },
      });
    }
  };

  const onAddGroupModelCancel = () => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        addGroupVisible: false,
        record: {},
      },
    });
  };

  const onOpenCrowdTransfer = record => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        record,
        crowdTransferVisible: true,
      },
    });
  };

  const onSubmitUpdateCreator = values => {
    dispatch({
      type: 'crowdCircle/editUpdateCreator',
      payload: values,
    });
  };

  const onCrowdTransferModalCancel = () => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        crowdTransferVisible: false,
        record: {},
      },
    });
  };

  const handleUpdateManager = value => {
    const data = {
      id: modelConfig.record.id,
      ...value,
    };

    if (!modelConfig.isOwner) {
      data['managers'] = data.owner;
      delete data['owner'];
    }
    dispatch({
      type: 'crowdCircle/updateCrowdCircleManager',
      payload: data,
    });
  };

  const handleUpdateCrowdGroup = value => {
    const data = {
      entityId: updateCrowdGroupConfig.record.id,
      groupIdList: value.groupIdList,
    };
    dispatch({
      type: 'crowdGroup/updateCrowdBatchGroup',
      payload: data,
    }).then(res => {
      if (res) {
        setUpdateCrowdGroupConfig({
          record: {},
          visible: false,
        });
        dispatch({
          type: 'crowdCircle/pageQuery',
          payload: {
            ...searchParams,
            tabKey,
          },
        });
        dispatch({
          type: 'crowdGroup/fetchData',
          payload: {
            pageNo: 1,
            pageSize: 10,
          },
        });
      }
    });
  };

  const onDetailModalCancel = () => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        detailModalVisible: false,
      },
    });
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        curCrowd: null,
      },
    });
  };

  const refresh = () => {
    dispatch({
      type: 'crowdCircle/queryCrowdById',
      payload: {
        refresh: true,
      },
    });
  };

  const onCrowdOperateModalCancel = () => {
    dispatch({
      type: 'crowdCircle/onCrowdByOperateModalCancel',
    });
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        curCrowd: null,
      },
    });
  };

  const onSubmitOperate = values => {
    dispatch({
      type: 'crowdCircle/editCrowdByOperate',
      payload: values,
    });
  };

  const onDynamicCrowdModalCancel = () => {
    dispatch({
      type: 'crowdCircle/updateState',
      payload: {
        dynamicCrowdModalVisible: false,
        editDynamicCrowdFormData: {},
      },
    });
  };

  const onSubmitDynamicCrowd = values => {
    dispatch({
      type: 'crowdCircle/editDynamicCrowd',
      payload: values,
    });
  };

  return (
    <PageHeaderWrapper
      title="人群圈选"
      toDocumentLink="https://aliyuque.antfin.com/qnwrq9/kzl68s/iadc1sxwfgwh18iu#"
    >
      <Card bordered={false} className={styles.searchCard}>
        <SearchFrorm />
      </Card>
      <Card className={styles.tableCard} bordered={false}>
        <Tabs
          tabBarExtraContent={
            <Button
              type="primary"
              onClick={() => {
                setCreateCrowdVisible(true);
              }}
            >
              创建人群
            </Button>
          }
          activeKey={tabKey}
          onChange={key => {
            // 保存 tabKey 到 localStorage，切换 tab 时重置为第1页
            try {
              const saved = localStorage.getItem('crowdCircle_searchParams');
              const params = saved ? JSON.parse(saved) : {};
              params.tabKey = key;
              params.pageNo = 1; // 切换 tab 时重置页码
              localStorage.setItem('crowdCircle_searchParams', JSON.stringify(params));
            } catch (error) {
              console.error('保存 tabKey 失败:', error);
            }

            dispatch({
              type: 'crowdCircle/updateState',
              payload: {
                tabKey: key,
              },
            });
            dispatch({
              type: 'crowdCircle/pageQuery',
              payload: {
                ...searchParams,
                tabKey: key,
                pageNo: 1,
              },
            });
          }}
        >
          {classifyTabList.map(ele => (
            <Tabs.TabPane tab={`${ele?.label}（${tabCntMap[ele?.value]}）`} key={ele?.value} />
          ))}
        </Tabs>
        <Row>
          <Col
            span={isFold ? 1 : 3}
            style={{
              transition: 'all 0.2s',
            }}
          >
            <LeftTree setIsFold={setIsFold} isFold={isFold} />
          </Col>
          <Col
            span={isFold ? 23 : 21}
            style={{
              transition: 'all 0.2s',
            }}
          >
            <Table
              columns={columns}
              scroll={{ x: 1300 }}
              rowKey="id"
              dataSource={dataSource}
              loading={loading}
              rowSelection={
                window.location.href.includes('needRadio=true')
                  ? {
                      type: 'radio',
                      onChange: (selectedRowKeys, selectedRows) => {
                        const obj = { id: selectedRowKeys[0], name: selectedRows[0].crowdName };
                        window.parent.postMessage(obj, '*');
                      },
                    }
                  : null
              }
              pagination={{
                total: tabCntMap[tabKey],
                current: searchParams.pageNo,
                pageSize: searchParams.pageSize,
                showSizeChanger: true,
                showQuickJumper: true,
                onChange: (pageNo, pageSize) => {
                  // 保存分页状态到 localStorage
                  try {
                    const saved = localStorage.getItem('crowdCircle_searchParams');
                    const params = saved ? JSON.parse(saved) : {};
                    params.pageNo = pageNo;
                    params.pageSize = pageSize;
                    localStorage.setItem('crowdCircle_searchParams', JSON.stringify(params));
                  } catch (error) {
                    console.error('保存分页状态失败:', error);
                  }

                  dispatch({
                    type: 'crowdCircle/pageQuery',
                    payload: {
                      ...searchParams,
                      pageNo,
                      pageSize,
                      tabKey,
                    },
                  });
                },
              }}
              expandable={{
                expandedRowKeys,
                onExpand: (expanded, record) => {
                  dispatch({
                    type: 'crowdCircle/updateState',
                    payload: {
                      expandedRowKeys: expanded
                        ? [...expandedRowKeys, record.id]
                        : expandedRowKeys.filter(item => item !== record.id),
                    },
                  });
                },
                defaultExpandAllRows: isChildSearch,
                expandedRowRender,
                expandIcon: ({ expanded, onExpand, record }) => {
                  return (
                    <div className={styles.expandContainer}>
                      <MyIcon
                        className={styles.icon}
                        type={record.isGrouped ? 'icon-star-full' : 'icon-star'}
                        onClick={() => {
                          if (dataList.length <= 0) {
                            dispatch({
                              type: 'crowdGroup/fetchData',
                              payload: {
                                pageSize: 500,
                              },
                            });
                          }
                          setUpdateCrowdGroupConfig({
                            visible: true,
                            record,
                          });
                          dispatch({
                            type: 'crowdGroup/queryCrowdGroupId',
                            payload: {
                              type: 'CROWD',
                              entityId: record.id,
                            },
                          }).then(res => {
                            let groupIdList = (res || []).length > 0 ? res : [];
                            crowdGroupForm.setFieldsValue({
                              groupIdList,
                            });
                          });
                        }}
                      />
                      {record.childCrowds && record.childCrowds.length > 0 ? (
                        <div style={{ cursor: 'pointer' }} onClick={e => onExpand(record, e)}>
                          {expanded ? <DownCircleFilled /> : <UpCircleFilled />}
                        </div>
                      ) : null}
                    </div>
                  );
                },
              }}
            />
          </Col>
        </Row>
      </Card>

      <CreateCrowdModal1
        editModalVisible={createCrowdVisible}
        onSubmit={(profileType, crowdType) => {
          if (crowdType === 'DYNAMIC_CROWD') {
            // 动态人群使用弹窗方式
            dispatch({
              type: 'crowdCircle/updateState',
              payload: {
                dynamicCrowdModalVisible: true,
                editDynamicCrowdFormData: { profileType },
              },
            });
            setCreateCrowdType('');
            setCreateCrowdVisible(false);
          } else {
            // 其他类型使用路由跳转
            history.push(
              `/crowd-stategy/gather-person/detail?crowdType=${crowdType}&profileType=${profileType}`
            );
          }
        }}
        onCancel={() => {
          setCreateCrowdType('');
          setCreateCrowdVisible(false);
        }}
      />

      {/* 延期弹窗 */}
      <CrowdDelayModal
        editFormData={record}
        editModalVisible={crowdDelayModalVisible}
        onCancel={onCrowdDelayModalCancel}
        onSubmit={onSubmitDelayTime}
      />

      <CrowdDetailModal
        detailLoading={detailLoading}
        curCrowd={curCrowd}
        visible={detailModalVisible}
        onCancel={onDetailModalCancel}
        refresh={refresh}
        loading={refreshLoading}
        crowdErrMsgMap={crowdErrMsgMap}
      />

      <CreateOperateCrowdModal
        detailLoading={detailLoading}
        editModalVisible={crowdByOperateModalVisible}
        editFormData={editCrowdByOperateFormData}
        onCancel={onCrowdOperateModalCancel}
        onSubmit={onSubmitOperate}
      />

      {/* 动态人群 */}
      <CreateDynamicCrowdModal
        editModalVisible={dynamicCrowdModalVisible}
        editFormData={editDynamicCrowdFormData}
        onCancel={onDynamicCrowdModalCancel}
        onSubmit={onSubmitDynamicCrowd}
      />

      {/* 测试 */}
      <CrowdTestModal
        editModalVisible={crowdTestModalVisible}
        editFormData={record}
        onCancel={onCrowdTestModalCancel}
      />

      {/** 导出 */}
      <CrowdExportModal
        isRealTime={isRealTime}
        isOwner={isOwner}
        editCrowdExportFormData={record}
        editModalVisible={crowdExportModalVisible}
        onCancel={onCrowdExportModalClose}
        onSubmit={onSubmitExportCrowd}
      />

      {/** 黑白名单 */}
      <CrowdBlackOrWhiteList
        BlackOrWhiteListFormData={record}
        editModalVisible={blackOrWhiteListModalVisible}
        onCancel={onCrowdBlackOrWhiteListModalClose}
        onSubmit={onSubmitBlackOrWhiteListCrowd}
      />

      {/** 监控告警 */}
      <CrowdMonitorModal
        visible={crowdMonitorVisible}
        onCancel={onCrowdMonitorCancel}
        onOk={onConfirmMonitor}
        crowdMonitorFormData={record?.list || []}
      />

      <AddGroupModel
        addGroupVisible={addGroupVisible}
        onCancel={onAddGroupModelCancel}
        onRefresh={defaultFetchData}
        groupModeFormData={record}
        bucketLoading={bucketLoading}
        dispatch={dispatch}
      />

      {/** 转交人群 */}
      <CrowdTransferModal
        editCrowdTransferFormData={record}
        editModalVisible={crowdTransferVisible}
        onCancel={onCrowdTransferModalCancel}
        onSubmit={onSubmitUpdateCreator}
      />

      <UpdateOwner onSubmit={handleUpdateManager} form={form} />
      <CrowdGroupModal
        updateCrowdGroupConfig={updateCrowdGroupConfig}
        setUpdateCrowdGroupConfig={setUpdateCrowdGroupConfig}
        crowdGroupForm={crowdGroupForm}
        dataList={dataList}
        handleUpdateCrowdGroup={handleUpdateCrowdGroup}
      />
    </PageHeaderWrapper>
  );
};

export default connect(state => ({
  user: state.user,
  crowdCircle: state.crowdCircle,
  crowdGroup: state.crowdGroup,
}))(CrowdCircle);
