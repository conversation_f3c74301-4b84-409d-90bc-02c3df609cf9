import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, DatePicker, Select, Button, message } from 'antd';
import { connect } from 'dva';
import dayjs from 'dayjs';
import GetUser from '@/components/GetUser';

const { TextArea } = Input;
const { Option } = Select;

const FORM_ITEM_LAYOUT = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 6 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
};

const CreateDynamicCrowdModal = props => {
  const {
    editModalVisible = false,
    editFormData = {},
    onSubmit = () => {},
    onCancel = () => {},
    crowdGroup: { dataList: groupList = [] },
    dispatch,
  } = props;

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 组件挂载时获取人群分组数据
  useEffect(() => {
    if (editModalVisible) {
      dispatch({
        type: 'crowdGroup/fetchData',
        payload: {
          pageNo: 1,
          pageSize: 100, // 获取更多分组数据
        },
      });
    }
  }, [editModalVisible, dispatch]);

  // 表单初始值设置
  useEffect(() => {
    if (editModalVisible && editFormData) {
      form.setFieldsValue({
        crowdName: editFormData.crowdName || '',
        crowdDescription: editFormData.crowdDescription || '',
        expiredDate: editFormData.expiredDate ? dayjs(editFormData.expiredDate) : dayjs().add(30, 'day'),
        groupIds: editFormData.groupIds || [],
      });
    }
  }, [editModalVisible, editFormData, form]);

  const handleSubmit = () => {
    form
      .validateFields()
      .then(values => {
        setLoading(true);
        
        const payload = {
          ...values,
          crowdType: 'DYNAMIC_CROWD',
          physicalProfileCode: editFormData.profileType || 'TAOBAO_USER',
          needUpdate: false, // 默认false
          expiredDate: values.expiredDate.valueOf(),
          applyScenes: ['MATCH'], // 默认应用场景
        };

        onSubmit(payload);
      })
      .catch(errorInfo => {
        console.log('表单验证失败:', errorInfo);
        setLoading(false);
      });
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="人群信息"
      visible={editModalVisible}
      destroyOnClose
      width={600}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
          创建
        </Button>,
      ]}
    >
      <Form {...FORM_ITEM_LAYOUT} form={form}>
        <Form.Item
          name="crowdName"
          label="人群名称"
          rules={[
            { required: true, message: '请输入人群名称' },
            { max: 50, message: '人群名称不能超过50个字符' },
          ]}
        >
          <Input placeholder="请输入人群名称" />
        </Form.Item>

        <Form.Item
          name="crowdDescription"
          label="人群描述"
          rules={[
            { required: true, message: '请输入人群描述' },
            { max: 200, message: '人群描述不能超过200个字符' },
          ]}
        >
          <TextArea
            placeholder="请输入人群描述"
            rows={3}
            showCount
            maxLength={200}
          />
        </Form.Item>

        <Form.Item
          name="expiredDate"
          label="过期时间"
          rules={[{ required: true, message: '请选择人群过期时间' }]}
        >
          <DatePicker
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择过期时间"
            style={{ width: '100%' }}
            disabledDate={current => current && current < dayjs().startOf('day')}
          />
        </Form.Item>

        <Form.Item
          name="groupIds"
          label="人群分组"
          rules={[{ required: true, message: '请选择人群分组' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择人群分组"
            style={{ width: '100%' }}
            allowClear
          >
            {groupList.map(group => (
              <Option key={group.key} value={group.key}>
                {group.title}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item label="人群包类型">
          <span>淘宝id</span>
        </Form.Item>

        <Form.Item label="应用场景">
          <span>人群匹配</span>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default connect(({ crowdGroup }) => ({
  crowdGroup,
}))(CreateDynamicCrowdModal);
