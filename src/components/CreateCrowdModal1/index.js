import React, { useState } from 'react';
import { Modal, Row, Col } from 'antd';
import CheckCard from '@/components/CheckCard'
import { get, cloneDeep } from 'lodash'

const CROWD_TYPE_MAP = [{
  name: '标签圈人',
  desc: '通过平台提供的标签进行“交并差”形式的操作，灵活创建人群包',
  value: 'LABEL_CROWD',
  isCommonUse: true
}, {
  name: '人群导入',
  desc: '通过ODPS导入、自定义SQL、文件上传、二方人群等方式，自定义创建人群包',
  value: 'IMPORT_CROWD'
}, {
  name: '人群组合',
  desc: '对已有多个人群包（实时/离线）之间进行“交并差”形式的操作，创建新的人群包',
  value: 'OPERATE_CROWD'
}, {
  name: '动态人群',
  desc: '创建动态更新的人群包，支持自动更新和过期管理',
  value: 'DYNAMIC_CROWD',
}]

const POPULATION_TYPE = [{
  name: '淘宝id',
  value: 'TAOBAO_USER',
  desc:'“淘宝id”指淘宝账户uid（飞猪是共用的手淘账户体系）'
},{
  name: '唯一设备id',
  desc: '“设备id”指用户手机的唯一设备码（如ios的idfa，Android的imei等）',
  value: 'DEVICE'
}]

const CreateCrowdModal = (props) => {
  const { onCancel, onSubmit, editModalVisible } = props;
  const [ crowdTypeMap, setCrowdTypeMap] = useState(CROWD_TYPE_MAP)
  const [ crowdPack, setCrowdPack] = useState('TAOBAO_USER')
  const [ crowdType, setCrowdType] = useState('LABEL_CROWD')

  const handleOk = () => {
    onSubmit(crowdPack, crowdType)
    onReset()
  };

  const handleCancel = () => {
    onCancel()
    onReset()
  }

  const onReset = () => {
    setCrowdPack('TAOBAO_USER')
    setCrowdType('LABEL_CROWD')
    setCrowdTypeMap(CROWD_TYPE_MAP)
  }

  const onCheckCrowdPack = (item) => {
    if (item.value === 'DEVICE') {
      setCrowdTypeMap(CROWD_TYPE_MAP.slice(0,1))
      setCrowdType('LABEL_CROWD')
    } else {
      setCrowdTypeMap(CROWD_TYPE_MAP)
    }
    setCrowdPack(item.value)
  }

  const onCheckCrowdType = (item) => {
    setCrowdType(item.value)
  }

  return (
    <Modal
      title="选择圈选方式"
      visible={editModalVisible}
      destroyOnClose={true}
      width={900}
      onOk={handleOk}
      onCancel={handleCancel}
    >
      <h3>人群包类型</h3>
      <Row gutter={[10, 10]}>
        {POPULATION_TYPE.map((item) => (
          <Col span={8} key={item.value} onClick={() => onCheckCrowdPack(item)}>
            <CheckCard
              name={item.name}
              desc={item.desc}
              disabled={item.value === crowdPack}
            />
          </Col>
        ))}
      </Row>
      <h3>圈选方式</h3>
      <Row gutter={[10, 10]}>
        {crowdTypeMap && crowdTypeMap.length > 0 && crowdTypeMap.map((item) => (
          <Col span={8} key={item.value} onClick={() => onCheckCrowdType(item)}>
            <CheckCard
              name={item.name}
              desc={item.desc}
              isCommonUse={item.isCommonUse}
              disabled={item.value === crowdType}
            />
          </Col>
        ))}
      </Row>
    </Modal>
  );
};

export default CreateCrowdModal;
